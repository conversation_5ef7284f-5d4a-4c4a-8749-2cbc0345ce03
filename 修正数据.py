
from aigcsdk.trace.train_trace import record_remaining_time


input_f = "./ytsoku_dev.ad_review_ljt_tmp.csv"
f = open(input_f, 'r', encoding='utf-8').read()

fixed_f = "./ytsoku_dev.ad_review_ljt_tmp_fixed.csv"
with open(fixed_f, 'w', encoding='utf-8') as f_out:
    lines = f.strip().split('\n')
    new_lines = []
    new_lines.append(lines[0])
    for line in lines[1:]:
        if line[8:15] == ',2025-0':
            new_lines.append(line)
        else:
            new_lines[-1] += line
    f_out.write('\n'.join(new_lines))

filtered_fixed_f = "./ytsoku_dev.ad_review_ljt_tmp_fixed_filtered.csv"
import pandas as pd
df = pd.read_csv(fixed_f, low_memory=False)
df = df[['creative_id', 'title', 'sub_title', 'image_url', 'videolongid', 'ldp_url_first', 'end_type', 'advertiser_id', 'industry_name']]
df.to_csv(filtered_fixed_f, index=False, encoding='utf-8')