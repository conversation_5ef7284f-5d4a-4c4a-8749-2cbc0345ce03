input_f = "./ytsoku_dev.ad_review_ljt_tmp.csv"
f = open(input_f, 'r', encoding='utf-8').read()

output_f = "./ytsoku_dev.ad_review_ljt_tmp_fixed.csv"
with open(output_f, 'w', encoding='utf-8') as f_out:
    lines = f.strip().split('\n')
    new_lines = []
    new_lines.append(lines[0])
    for line in lines[1:]:
        if line[8:15] == ',2025-0':
            new_lines.append(line)
        else:
            new_lines[-1] += line
    f_out.write('\n'.join(new_lines))
