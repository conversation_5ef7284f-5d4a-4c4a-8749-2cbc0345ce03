import requests
import json
from typing import Dict, Any, Optional

SERVER_URL = "http://pre-aigc-gw.youku.com/v1/models/predict"
GET_TASK_RESULT_URL = "http://pre-aigc-gw.youku.com/v1/tasks/getTaskResult"
def request_aigc_service(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    向AIGC服务发送请求

    Args:
        payload: 请求的payload数据，包含以下字段：
            - request_id: 请求ID
            - creative_id: 创意ID
            - industry: 行业
            - title: 标题
            - image_url: 图片URL列表

    Returns:
        Dict: 服务返回的响应数据

    Raises:
        requests.RequestException: 请求异常
        ValueError: 响应数据解析异常
    """

    # 服务端点
    url = ""

    # 请求头
    headers = {
        "Content-Type": "application/json"
    }

    # 构建完整的请求数据
    request_data = {
        "appId": "youku-algo-dev-zjk",
        "modelId": "demo-zq",
        "reqType": "async",
        "bizCode": "video-gen",
        "payload": payload
    }

    try:
        # 发送POST请求
        response = requests.post(
            url=url,
            headers=headers,
            json=request_data,
            timeout=30  # 30秒超时
        )

        # 检查HTTP状态码
        response.raise_for_status()

        # 解析JSON响应
        result = response.json()

        return result

    except requests.exceptions.Timeout:
        raise requests.RequestException("请求超时")
    except requests.exceptions.ConnectionError:
        raise requests.RequestException("连接错误")
    except requests.exceptions.HTTPError as e:
        raise requests.RequestException(f"HTTP错误: {e}")
    except json.JSONDecodeError:
        raise ValueError("响应数据不是有效的JSON格式")
    except Exception as e:
        raise requests.RequestException(f"请求失败: {e}")




def create_sample_payload() -> Dict[str, Any]:
    """
    创建示例payload数据

    Returns:
        Dict: 示例payload
    """
    return {
        "request_id": "123",
        "creative_id": "123",
        "industry": "test",
        "title": "test",
        "image_url": ["https://acg.youku.com/webfile/Gv4XSuCPcCK3D6rs8W41izaQnh8UFjoq.jpg"]
    }


if __name__ == "__main__":
    # 测试示例
    sample_payload = create_sample_payload()

    try:
        result = request_aigc_service(sample_payload)
        print("请求成功:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"请求失败: {e}")
