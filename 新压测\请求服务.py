import json
import requests
import uuid
from request_tool import AIGCServiceClient, create_sample_payload
import pandas as pd
import uuid
import json
import random
import time
import traceback
import tqdm









def from_row_to_payload(row):
    # creative_id,gmt_create,ds,tr,advertiser_id,status,status_desc,cnt,
    # video_url,image_url,title,sub_title,end_type,audit_score,audit_score_v2,
    # label_id_list,ldp_url_first,status_auto,status_manual,time_rank,
    # zizhi_modified,industry_name,first_industry_code_v2,second_industry_code_v2,
    # third_industry_code_v2,first_industry,second_industry,certification_list
    # payload = {
    #     "request_id": str(uuid.uuid4()),
    #     "creative_id": row["creative_id"],
    #     "industry":  row["industry_name"],
    #     "title": [row["title"],row["sub_title"]],
    #     "image_url": [row["image_url"]],
    #     "video_id": [row["videolongid"]],
    #     "ldp_url": row["ldp_url_first"],
    #     "device_type": row["end_type"],
    #     "advertiser_id": row["advertiser_id"],
    #     # "license_info": row["certification_list"]
    # }
    payload = {
        "name": str(uuid.uuid4()),
        "parameters": {
            "request":{
                "logo": [],
                "title": row["title"],
                "ldpUrl": row["ldp_url_first"],
                "videoId": row["videolongid"],
                "imageUrl": [row["image_url"]],
                "industry": row["industry_name"],
                "subTitle": row["sub_title"],
                "logoTitle": [],
                "creativeId": row["creative_id"],
                "deviceType": "mobile",
                "licenseInfo": [],
                "advertiserId": row['advertiser_id'],
                "placementType": ["feed"]
            }
        }
    }
    for key in ("image_url", "video_id"):
        if payload[key][0] is None:
            payload[key] = None
    if payload["video_id"]:
        payload["video_id"] = int(payload["video_id"])
    if payload["advertiserId"]:
        payload["advertiserId"] = [payload["advertiserId"]]
    if payload["creativeId"]:
        payload["creativeId"] = [payload["creativeId"]]
    return payload

def test_data_loader(csv_file_path, sample_size=None):
    # 指定dtype和low_memory参数避免DtypeWarning
    start_time = time.time()
    df = pd.read_csv(csv_file_path, low_memory=False)
    if sample_size:
        df = df.sample(sample_size, random_state=42)
    print(f"读取数据耗时: {time.time() - start_time:.2f}秒")

    # 使用直接赋值方式避免FutureWarning
    # row["industry_name"] = df["industry_name"].fillna("其他")
    df["title"] = df["title"].fillna("")
    df["sub_title"] = df["sub_title"].fillna("")
    df["image_url"] = df["image_url"].fillna(" ")
    df["image_url"] = df["image_url"].apply(lambda x: None if x == " " else x)
    df["videolongid"] = df["videolongid"].fillna(" ")
    df["videolongid"] = df["videolongid"].apply(lambda x: None if x == " " else x)

    for _, row in df.iterrows():
        yield row

def 压力测试_from_odps_csv(csv_file_path, mid_res_file_path):
    """
    从odps csv文件中读取数据，进行压力测试。csv文件中的每一行数据都发送一个请求。
    将每行的creative_id和请求返回的taskId，保存在一个mid_res_file_path中,后续可以用来查询结果。
    """


def get_dict_all_values_recursively(d):
    for v in d.values():
        if isinstance(v, dict):
            yield from get_dict_all_values_recursively(v)
        elif isinstance(v, list):
            for x in v:
                yield x
        else:
            yield v

if __name__ == "__main__":
    request_phase = True
    query_phase = False
    client = AIGCServiceClient()

    input_file = "./ytsoku_dev.ad_review_ljt_tmp_fixed_filtered.csv"
    task_id_file = "./task_ids_0801.csv"

    if request_phase:
        loader = test_data_loader(input_file, 1500)
        task_mid_infos = []
        for row in tqdm(loader):
            payload = from_row_to_payload(row)
            # print('\n', '='*40, '\n')
            try:
                json.dumps(payload, ensure_ascii=False, indent=2, allow_nan=False)
            except:
                print(payload)
                exit()
            try:
                task_id = 1#client.submit_task(payload)
            except:
                print(traceback.format_exc())
                task_id = 1
            creative_id = payload["creative_id"]
            task_mid_infos.append([creative_id, task_id])
        df = pd.DataFrame(task_mid_infos, columns=["creative_id", "task_id"])
        # df.to_csv(task_id_file, index=False)
    if query_phase:
        task_mid_infos = pd.read_csv(task_id_file)
        for _, row in task_mid_infos.iterrows():
            creative_id = row["creative_id"]
            task_id = row["task_id"]
            status, res = client.get_task_result(task_id)
            print(creative_id, status, res)




