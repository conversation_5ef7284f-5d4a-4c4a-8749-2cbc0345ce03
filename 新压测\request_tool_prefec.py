import requests
import json
import time
from typing import Dict, Any, Tuple
from enum import Enum

SERVER_URL = 'https://pre-youku-prefect-v1.alibaba-inc.com/api/deployments/a462b4e4-43f1-4b00-b108-549854659044/create_flow_run'
GET_TASK_RESULT_URL = "https://pre-youku-prefect-v1.alibaba-inc.com/api/flow_runs/"



response = requests.post(url, json=payload)
if response.status_code == 201:
    data = response.json()
    json.dump(data, open(f'./新压测/{data["id"]}.json', 'w'))
    print('请求成功')
else:
    raise Exception(f"task_create_gen_flow failed, deploy_id: {deploy_id}, arg:{payload}")


class TaskStatus(Enum):
    """任务状态枚举"""
    SCHEDULED,PENDING,RUNNING,PAUSED,CANCELLING
以下表示已结束：
CANCELLED,COMPLETED,FAILED,CRASHED
    QUEUING = (1, "排队中")
    RUNNING = (2, "处理中")
    SUCCESS = (3, "处理成功")
    FAIL = (4, "处理失败")

    def __init__(self, code: int, description: str):
        self.code = code
        self.description = description

    @classmethod
    def from_code(cls, code: int) -> 'TaskStatus':
        """根据状态码获取任务状态"""
        for status in cls:
            if status.code == code:
                return status
        raise ValueError(f"未知的任务状态码: {code}")


class AIGCServiceClient:
    """AIGC服务客户端"""

    def __init__(self, timeout: int = 30):
        """
        初始化客户端

        Args:
            timeout: 请求超时时间（秒）
        """
        self.timeout = timeout
        self.headers = {"Content-Type": "application/json"}

    def submit_task(self, payload: Dict[str, Any]) -> str:
        """
        提交任务到AIGC服务

        Args:
            payload: 请求的payload数据

        Returns:
            str: 任务ID

        Raises:
            requests.RequestException: 请求异常
            ValueError: 响应数据解析异常
        """
        request_data = payload

        try:
            response = requests.post(
                url=SERVER_URL,
                headers=self.headers,
                json=request_data,
                timeout=self.timeout
            )

            response.raise_for_status()
            result = response.json()

            # 提取taskId
            if "taskId" in result:
                return result["taskId"]
            else:
                raise ValueError("响应中未找到taskId字段")

        except requests.exceptions.Timeout:
            raise requests.RequestException("请求超时")
        except requests.exceptions.ConnectionError:
            raise requests.RequestException("连接错误")
        except requests.exceptions.HTTPError as e:
            raise requests.RequestException(f"HTTP错误: {e}")
        except json.JSONDecodeError:
            raise ValueError("响应数据不是有效的JSON格式")
        except Exception as e:
            raise requests.RequestException(f"提交任务失败: {e}")

    def get_task_result(self, task_id: str) -> Tuple[str, Dict[str, Any]]:
        """
        获取任务执行结果

        Args:
            task_id: 任务ID

        Returns:
            Tuple[str, Dict]: (任务状态描述, 完整响应数据)

        Raises:
            requests.RequestException: 请求异常
            ValueError: 响应数据解析异常
        """
        try:
            response = requests.get(
                url=GET_TASK_RESULT_URL+task_id,
                timeout=self.timeout
            )

            response.raise_for_status()
            result = response.json()

            # 获取任务状态
            if "state_type" in result:
                state_type = result["state_type"]

            return state_type, result

        except requests.exceptions.Timeout:
            raise requests.RequestException("请求超时")
        except requests.exceptions.ConnectionError:
            raise requests.RequestException("连接错误")
        except requests.exceptions.HTTPError as e:
            raise requests.RequestException(f"HTTP错误: {e}")
        except json.JSONDecodeError:
            raise ValueError("响应数据不是有效的JSON格式")
        except Exception as e:
            raise requests.RequestException(f"获取任务结果失败: {e}")

    def wait_for_completion(self, task_id: str, max_wait_time: int = 300,
                          poll_interval: int = 5) -> Tuple[str, Dict[str, Any]]:
        """
        等待任务完成（轮询方式）

        Args:
            task_id: 任务ID
            max_wait_time: 最大等待时间（秒）
            poll_interval: 轮询间隔（秒）

        Returns:
            Tuple[str, Dict]: (最终任务状态描述, 完整响应数据)

        Raises:
            TimeoutError: 等待超时
            requests.RequestException: 请求异常
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            status_desc, result = self.get_task_result(task_id)

            # 检查是否完成（成功或失败）
            if "taskStatus" in result:
                status_code = result["taskStatus"]
                if status_code in [TaskStatus.SUCCESS.code, TaskStatus.FAIL.code]:
                    return status_desc, result

            # 等待下次轮询
            time.sleep(poll_interval)

        raise TimeoutError(f"任务{task_id}在{max_wait_time}秒内未完成")

    def submit_and_wait(self, payload: Dict[str, Any], max_wait_time: int = 300,
                       poll_interval: int = 5) -> Tuple[str, str, Dict[str, Any]]:
        """
        提交任务并等待完成（一站式服务）

        Args:
            payload: 请求的payload数据
            max_wait_time: 最大等待时间（秒）
            poll_interval: 轮询间隔（秒）

        Returns:
            Tuple[str, str, Dict]: (任务ID, 最终状态描述, 完整响应数据)
        """
        # 提交任务
        task_id = self.submit_task(payload)

        # 等待完成
        status_desc, result = self.wait_for_completion(
            task_id, max_wait_time, poll_interval
        )

        return task_id, status_desc, result


def create_sample_payload() -> Dict[str, Any]:
    """
    创建示例payload数据

    Returns:
        Dict: 示例payload
    """
    return {
        "request_id": "123",
        "creative_id": "123",
        "industry": "test",
        "title": "test",
        "image_url": ["https://acg.youku.com/webfile/Gv4XSuCPcCK3D6rs8W41izaQnh8UFjoq.jpg"]
    }


if __name__ == "__main__":
    # 测试示例 - 使用新的类接口
    print("=== 使用AIGCServiceClient类 ===")
    client = AIGCServiceClient()
    sample_payload = create_sample_payload()

    status, res = client.get_task_result("12c94f66-407d-4017-8779-d937190b26bc")
    print(status, json.dumps(res, indent=2, ensure_ascii=False))
    exit()

    try:
        # 方式1: 分步操作
        print("1. 提交任务...")
        task_id = client.submit_task(sample_payload)
        print(f"任务ID: {task_id}")

        print("2. 查询任务状态...")
        status, result = client.get_task_result(task_id)
        print(f"当前状态: {status}")
        print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")

        # 方式2: 一站式操作（提交并等待完成）
        print("\n3. 一站式操作（提交并等待完成）...")
        task_id, final_status, final_result = client.submit_and_wait(
            sample_payload, max_wait_time=60, poll_interval=3
        )
        print(f"任务ID: {task_id}")
        print(f"最终状态: {final_status}")
        print(f"最终结果: {json.dumps(final_result, indent=2, ensure_ascii=False)}")

    except Exception as e:
        print(f"操作失败: {e}")

    print("\n=== 使用兼容性函数 ===")
