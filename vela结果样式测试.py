import requests
import time

url = "http://pre-aigc-gw.youku.com/v1/models/predict"
time_out =300
def request_auido_vela( audio_url):
        data = {
            "appId": "youku-algo-dev-zjk",
            "modelId": "demo-zq",
            "reqType": "async",
            "bizCode": "video-gen",
            "payload": {"audio_url": audio_url, "model_type": "VIDEO_TEXT"},
        }
        # 发送POST请求，使用json参数自动设置Content-Type
        response = requests.post(url, json=data)

        # 检查响应状态码
        response.raise_for_status()

        # 解析响应的JSON数据
        response_data = response.json()
        task_id = response_data["taskId"]
        return task_id
def get_vela_result(task_id):
        # 轮询结果
        start  = time.time()
        while True:
            response = requests.get(f"http://pre-aigc-gw.youku.com/v1/tasks/getTaskResult?taskId={task_id}")
            response.raise_for_status()
            response_data = response.json()

            if response_data["taskStatusEnum"] == "SUCCESS":
                break
            if time.time() - start >= time_out:
                print("vela 请求超时")
                return ""


            time.sleep(0.01)

        # 输出响应数据
        return response_data["result"]["request"]

audio_url = "oss://youkudnnmgv1/machine_review/audio/48523bd6624c52a640c88808f7b95ccc.wav"
task_id = request_auido_vela(audio_url)
print(get_vela_result(task_id))