import pandas as pd

df = pd.read_csv("./结果.csv", encoding='utf-16')
df = df.sort_values(by='LastModifiedTime', ignore_index=True)
df = df.tail(1500)


print(f'起始时间为：{df["LastModifiedTime"].min()}')
print(f'结束时间为：{df["LastModifiedTime"].max()}')
# 起始时间为：2025-08-01 19:39:24 +0800 CST
# 结束时间为：2025-08-04 16:23:58 +0800 CST
# 处理包含时区信息的时间格式 - 先移除CST部分，只保留时区偏移
df["LastModifiedTime_clean"] = df["LastModifiedTime"].str.replace(" CST", "")
df["LastModifiedTime_parsed"] = pd.to_datetime(df["LastModifiedTime_clean"], format='%Y-%m-%d %H:%M:%S %z')

# 计算统计信息
total_requests = len(df)
start_time = df["LastModifiedTime_parsed"].min()
end_time = df["LastModifiedTime_parsed"].max()
total_time_seconds = (end_time - start_time).total_seconds()
total_time_minutes = total_time_seconds / 60
total_time_hours = total_time_minutes / 60
avg_time_per_request = total_time_seconds / total_requests

# 输出统计信息
print(f'总请求条数：{total_requests}条')
print(f'总耗时：{total_time_seconds:.2f}秒 ({total_time_minutes:.2f}分钟 / {total_time_hours:.2f}小时)')
print(f'平均每条请求耗时：{avg_time_per_request:.2f}秒')
print(f'平均每秒处理请求数：{total_requests / total_time_seconds:.2f}条/秒')
# print(df)