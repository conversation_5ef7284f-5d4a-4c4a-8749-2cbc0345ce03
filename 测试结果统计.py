import pandas as pd

def format_seconds(seconds):
    """将秒数转换为易读的时间格式"""
    if seconds < 60:
        return f"{seconds:.2f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}分{remaining_seconds:.1f}秒"
    else:
        hours = int(seconds // 3600)
        remaining_minutes = int((seconds % 3600) // 60)
        remaining_seconds = seconds % 60
        return f"{hours}小时{remaining_minutes}分{remaining_seconds:.1f}秒"

df = pd.read_csv("./结果.csv", encoding='utf-16')
df = df.sort_values(by='LastModifiedTime', ignore_index=True)
df = df.tail(1500)


print(f'起始时间为：{df["LastModifiedTime"].min()}')
print(f'结束时间为：{df["LastModifiedTime"].max()}')
# 起始时间为：2025-08-01 19:39:24 +0800 CST
# 结束时间为：2025-08-04 16:23:58 +0800 CST
# 处理包含时区信息的时间格式 - 先移除CST部分，只保留时区偏移
df["LastModifiedTime_clean"] = df["LastModifiedTime"].str.replace(" CST", "")
df["LastModifiedTime_parsed"] = pd.to_datetime(df["LastModifiedTime_clean"], format='%Y-%m-%d %H:%M:%S %z')

# 计算统计信息
total_requests = len(df)
start_time = df["LastModifiedTime_parsed"].min()
end_time = df["LastModifiedTime_parsed"].max()
total_time_seconds = (end_time - start_time).total_seconds()
total_time_minutes = total_time_seconds / 60
total_time_hours = total_time_minutes / 60
avg_time_per_request = total_time_seconds / total_requests

# 计算请求间隔统计
df_sorted = df.sort_values('LastModifiedTime_parsed').reset_index(drop=True)
time_intervals = df_sorted['LastModifiedTime_parsed'].diff().dt.total_seconds().dropna()

min_interval = time_intervals.min()
max_interval = time_intervals.max()
median_interval = time_intervals.median()
mean_interval = time_intervals.mean()
std_interval = time_intervals.std()

# 计算详细分位数
percentiles = [25, 50, 75, 90, 95, 99, 99.9]
percentile_values = [p/100 for p in percentiles]
interval_percentiles = time_intervals.quantile(percentile_values)

# 创建分位数字典，方便访问
percentile_dict = dict(zip(percentile_values, interval_percentiles.values))

# 输出统计信息
print('='*50)
print('📊 压测统计报告')
print('='*50)
print(f'总请求条数：{total_requests}条')
print(f'总耗时：{total_time_seconds:.2f}秒 ({total_time_minutes:.2f}分钟 / {total_time_hours:.2f}小时)')
print(f'平均每条请求耗时：{avg_time_per_request:.2f}秒')
print(f'平均每秒处理请求数：{total_requests / total_time_seconds:.2f}条/秒')
print()
print('⏱️ 请求间隔统计：')
print(f'最小间隔：{min_interval:.4f}秒 ({format_seconds(min_interval)})')
print(f'最大间隔：{max_interval:.2f}秒 ({format_seconds(max_interval)})')
print(f'平均间隔：{mean_interval:.4f}秒 ({format_seconds(mean_interval)})')
print(f'中位数间隔：{median_interval:.4f}秒 ({format_seconds(median_interval)})')
print(f'间隔标准差：{std_interval:.4f}秒 ({format_seconds(std_interval)})')
print()
print('� 间隔分位数统计：')
for p in percentiles:
    interval_val = percentile_dict[p/100]
    speed_val = 1/interval_val if interval_val > 0 else float('inf')
    print(f'P{p:4.1f}: {interval_val:.4f}秒 ({format_seconds(interval_val)}) (处理速度: {speed_val:.2f}条/秒)')
print()
print('�📈 性能指标：')
print(f'最高处理速度：{1/min_interval:.2f}条/秒 (基于最小间隔)')
print(f'最低处理速度：{1/max_interval:.2f}条/秒 (基于最大间隔)')
print(f'中位处理速度：{1/median_interval:.2f}条/秒 (基于中位间隔)')
print()