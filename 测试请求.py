from request_tool import AIGCServiceClient, create_sample_payload
import pandas as pd
import uuid
import json
import random

def from_row_to_payload(row):
    # creative_id,gmt_create,ds,tr,advertiser_id,status,status_desc,cnt,
    # video_url,image_url,title,sub_title,end_type,audit_score,audit_score_v2,
    # label_id_list,ldp_url_first,status_auto,status_manual,time_rank,
    # zizhi_modified,industry_name,first_industry_code_v2,second_industry_code_v2,
    # third_industry_code_v2,first_industry,second_industry,certification_list
    payload = {
        "request_id": str(uuid.uuid4()),
        "creative_id": row["creative_id"],
        "industry":  row["industry_name"],
        "title": [row["title"],row["sub_title"]],
        "image_url": [row["image_url"]],
        "video_id": [row["videolongid"]],
        "ldp_url": row["ldp_url_first"],
        "device_type": row["end_type"],
        "advertiser_id": row["advertiser_id"],
        # "license_info": row["certification_list"]
    }
    for key in ("image_url", "video_id"):
        if payload[key][0] == ' ':
            payload[key] = None
    return payload
    # # 创意id
    # creative_id: int
    # # 行业
    # industry: str
    # # 文本标题
    # title: list[str]
    # # 图像url
    # image_url: list[str]
    # # 视频vid
    # video_id: list[int]
    # # 落地页
    # ldp_url: str
    # # 端类型
    # device_type: str
    # # 广告主id
    # advertiser_id: int
    # # 资质信息
    # license_info: list[dict]
    # # 唯一的请求ID。用于日志追踪和问题排查
    # request_id: str

def test_data_loader(csv_file_path):
    df = pd.read_csv(csv_file_path)
    df["title"].fillna("", inplace=True)
    df["sub_title"].fillna("", inplace=True)
    df["image_url"].fillna(" ", inplace=True)
    df["image_url"] = df["image_url"].apply(lambda x: None if x == " " else x)
    df["videolongid"].fillna(" ", inplace=True)
    df["videolongid"] = df["videolongid"].apply(lambda x: None if x == " " else x)

    for idx, row in df.iterrows():
        yield row

def 压力测试_from_odps_csv(csv_file_path, mid_res_file_path):
    """
    从odps csv文件中读取数据，进行压力测试。csv文件中的每一行数据都发送一个请求。
    将每行的creative_id和请求返回的taskId，保存在一个mid_res_file_path中,后续可以用来查询结果。
    """


if __name__ == "__main__":
    client = AIGCServiceClient()

    loader = test_data_loader("./ytsoku_dev.ad_review_ljt_tmp_fixed.csv")
    payloads = []
    for row in loader:
        payload = from_row_to_payload(row)
        payloads.append(payload)
    random.shuffle(payloads)
    for payload in payloads[:10]:
        print(json.dumps(payload, ensure_ascii=False, indent=2))

